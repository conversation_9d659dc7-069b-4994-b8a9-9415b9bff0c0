
import org.apache.http.HttpEntity;
import org.apache.http.HttpHost;
import org.apache.http.client.config.RequestConfig;
import org.apache.http.config.Registry;
import org.apache.http.config.RegistryBuilder;
import org.apache.http.conn.socket.ConnectionSocketFactory;
import org.apache.http.conn.socket.LayeredConnectionSocketFactory;
import org.apache.http.conn.socket.PlainConnectionSocketFactory;
import org.apache.http.conn.ssl.SSLConnectionSocketFactory;
import org.apache.http.conn.ssl.TrustStrategy;
import org.apache.http.entity.ByteArrayEntity;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.NoConnectionReuseStrategy;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.DefaultConnectionKeepAliveStrategy;
import org.apache.http.impl.client.HttpClientBuilder;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.impl.conn.PoolingHttpClientConnectionManager;
import org.apache.http.ssl.SSLContexts;
import org.apache.http.util.EntityUtils;


import javax.net.ssl.SSLContext;
import java.io.IOException;
import java.net.URI;
import java.security.KeyStore;
import java.util.HashMap;
import java.util.Map;

/**
 * Created by maoyule on 2018/4/26.
 */

public class WebClient {

    public static final int STATUS_OK = 200;
    private CloseableHttpClient httpclient;
    private CloseableHttpClient noConnectionReuseHttpclient;
    private RequestConfig config;

    private static final Map<String, String> DEF_HEADER_MAP = new HashMap<String, String>() {
        {
            put("Content-Type", "application/json");
        }
    };

    public WebClient() {
        init();
    }

    public WebClient(int connectTimeout, int socketTimeout) {
        init(connectTimeout, socketTimeout);
    }

    public void init() {
        init(2000, 5000);
    }

    /**
     * @param connectTimeout 连接超时 ms
     * @param socketTimeout  阅读超时 ms
     */
    public void init(int connectTimeout, int socketTimeout) {
        PoolingHttpClientConnectionManager connManager
                = createConnectionManager();

        /**
         * 连接数相关设置
         */
        //最大连接数
        connManager.setMaxTotal(200);
        //默认的每个路由的最大连接数
        connManager.setDefaultMaxPerRoute(200);
//        //设置到某个路由的最大连接数，会覆盖defaultMaxPerRoute
//        connManager.setMaxPerRoute(new HttpRoute(new HttpHost("somehost", 80)), 150);

        if (connectTimeout < 2000) {
            connectTimeout = 2000;
        }
        if (socketTimeout < 3000) {
            socketTimeout = 3000;
        }
        HttpClientBuilder httpClientBuilder = HttpClients.custom().setKeepAliveStrategy(new DefaultConnectionKeepAliveStrategy())
                .setConnectionManager(connManager);
        HttpHost proxyHost = getProxy();
        if (proxyHost != null) {
            httpclient = httpClientBuilder.setProxy(proxyHost).build();
        } else {
            httpclient = httpClientBuilder.build();
        }
        config = RequestConfig.custom()
                .setConnectTimeout(connectTimeout).setConnectionRequestTimeout(connectTimeout)
                .setSocketTimeout(socketTimeout).build();

        noConnectionReuseHttpclient = HttpClients.custom().setDefaultRequestConfig(config)
                .setConnectionReuseStrategy(NoConnectionReuseStrategy.INSTANCE)
                .setConnectionManager(connManager)
                .build();
    }

    private HttpHost getProxy() {
        String host = System.getProperty("http.proxyHost");
        String port = System.getProperty("http.proxyPort");
        if (host == null || port == null) {
            return null;
        }
        return new HttpHost(host, Integer.parseInt(port));
    }

    private PoolingHttpClientConnectionManager createConnectionManager() {
        RegistryBuilder<ConnectionSocketFactory> registryBuilder = RegistryBuilder.<ConnectionSocketFactory>create();
        ConnectionSocketFactory plainSF = new PlainConnectionSocketFactory();
        registryBuilder.register("http", plainSF);
        // 指定信任密钥存储对象和连接套接字工厂
        try {
            KeyStore trustStore = KeyStore.getInstance(KeyStore.getDefaultType());
            // 信任任何链接
            TrustStrategy anyTrustStrategy = new TrustStrategy() {

                @Override
                public boolean isTrusted(java.security.cert.X509Certificate[] arg0, String arg1) throws java.security.cert.CertificateException {
                    // TODO Auto-generated method stub
                    return true;
                }
            };
            SSLContext sslContext = SSLContexts.custom().loadTrustMaterial(trustStore, anyTrustStrategy).build();
            LayeredConnectionSocketFactory sslSF = new SSLConnectionSocketFactory(sslContext, SSLConnectionSocketFactory.ALLOW_ALL_HOSTNAME_VERIFIER);
            registryBuilder.register("https", sslSF);
        } catch (Exception e) {

        }

        Registry<ConnectionSocketFactory> registry = registryBuilder.build();
        // 设置连接管理器
        return new PoolingHttpClientConnectionManager(registry);
    }

    public HttpResponseData<String> sendGet(String url, Map<String, String> headerMap) {
        try {
            HttpGet httpget = new HttpGet(url);
            httpget.setConfig(config);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    httpget.addHeader(entry.getKey(), entry.getValue());
                }
            }
            CloseableHttpResponse response = httpclient.execute(httpget);
            try {
                HttpEntity entity = response.getEntity();
                String retString = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(entity);
                HttpResponseData<String> responseData = new HttpResponseData<>();
                responseData.setStatus(response.getStatusLine().getStatusCode());
                responseData.setBody(retString);
                return responseData;
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public byte[] getBytes(String url) {
        try {
            HttpGet httpget = new HttpGet(url);
            httpget.setConfig(config);
            CloseableHttpResponse response = httpclient.execute(httpget);
            try {
                HttpEntity entity = response.getEntity();
                byte[] bytes = EntityUtils.toByteArray(entity);
                EntityUtils.consume(entity);
                if (response.getStatusLine().getStatusCode() == STATUS_OK) {
                    return bytes;
                } else {
                    logger.error("Login form get: " + response.getStatusLine());
                    return null;
                }
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public String sendPost(String url, Map<String, String> params, int sendLimit) {
        try {
            // 发送限制，最小1次，最多10次
            if (sendLimit <= 0) {
                sendLimit = 1;
            } else if (sendLimit > 10) {
                sendLimit = 10;
            }
            RequestBuilder builder = RequestBuilder.post()
                    .setUri(new URI(url));
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addParameter(entry.getKey(), entry.getValue());
            }
            builder.addHeader("Content-Type", "application/x-www-form-urlencoded");
            builder.setConfig(config);
            HttpUriRequest urlRequest = builder.build();
            int testIndex = 0;
            CloseableHttpResponse response = null;
            while (testIndex < sendLimit) {
                testIndex++;
                try {
                    msgLogger.info("send to url={}, body={}", url, params);
                    response = httpclient.execute(urlRequest);
                    break;
                } catch (Exception e) {
                    if (testIndex == sendLimit) {
                        urlRequest.abort();
                        throw e;
                    } else {
                        logger.error("testCount:{}", testIndex, e);
                    }
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                String retString = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(entity);
                if (response.getStatusLine().getStatusCode() == STATUS_OK) {
                    return retString;
                } else {
                    logger.error("post error: {}", response.getStatusLine());
                    return retString;
                }
            } catch (Exception e) {
                logger.error("", e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "";
    }

    public String sendPost(String url, Map<String, String> params, Map<String, String> header) {
        try {
            RequestBuilder builder = RequestBuilder.post()
                    .setUri(new URI(url));
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addParameter(entry.getKey(), entry.getValue());
            }
            builder.addHeader("Content-Type", "application/x-www-form-urlencoded");
            for (String name : header.keySet()) {
                builder.addHeader(name, header.get(name));
            }
            builder.setConfig(config);
            HttpUriRequest urlRequest = builder.build();
            int testIndex = 0;
            CloseableHttpResponse response = null;
            while (testIndex < 1) {
                testIndex++;
                try {
                    msgLogger.info("send to url={}, body={}", url, params);
                    response = httpclient.execute(urlRequest);
                    break;
                } catch (Exception e) {
                    logger.error("testCount:{}", testIndex, e);
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                String retString = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(entity);
                if (response.getStatusLine().getStatusCode() == STATUS_OK) {
                    return retString;
                } else {
                    logger.error("post error: {}", response.getStatusLine());
                    return retString;
                }
            } catch (Exception e) {
                logger.error("", e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return "";
    }

    public HttpResponseData<String> sendPostWithHttpResp(String url, Map<String, String> params, int sendLimit) {
        try {
            // 发送限制，最小1次，最多10次
            if (sendLimit <= 0) {
                sendLimit = 1;
            } else if (sendLimit > 10) {
                sendLimit = 10;
            }
            RequestBuilder builder = RequestBuilder.post()
                    .setUri(new URI(url));
            for (Map.Entry<String, String> entry : params.entrySet()) {
                builder.addParameter(entry.getKey(), entry.getValue());
            }
            builder.addHeader("Content-Type", "application/x-www-form-urlencoded");
            builder.setConfig(config);
            HttpUriRequest urlRequest = builder.build();
            int testIndex = 0;
            CloseableHttpResponse response = null;
            while (testIndex < sendLimit) {
                testIndex++;
                try {
                    msgLogger.info("send to url={}, body={}", url, params);
                    response = httpclient.execute(urlRequest);
                    break;
                } catch (Exception e) {
                    if (testIndex == sendLimit) {
                        urlRequest.abort();
                        throw e;
                    } else {
                        logger.error("testCount:{}", testIndex, e);
                    }
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                String retString = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(entity);
                HttpResponseData<String> responseData = new HttpResponseData<>();
                responseData.setStatus(response.getStatusLine().getStatusCode());
                responseData.setBody(retString);
                return responseData;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public HttpResponseData<String> sendRestfulPost(String url, String json) {
        return sendRestfulPost(url, json, DEF_HEADER_MAP);
    }

    public HttpResponseData<String> sendRestfulPost(String url, String json, Map<String, String> headerMap) {
        return sendRestfulPost(url, json, headerMap, 3);
    }

    public HttpResponseData<String> sendRestfulPost(String url, String json, Map<String, String> headerMap, int sendLimit) {
        if (sendLimit < 1) {
            sendLimit = 1;
        }
        try {
            HttpPost post = new HttpPost(url);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    post.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity reqEntity = new StringEntity(json, ContentType.APPLICATION_JSON);
            post.setEntity(reqEntity);
            int testIndex = 0;
            // 防止卡死
            post.setConfig(config);
            CloseableHttpResponse response = null;
            while (testIndex < sendLimit) {
                testIndex++;
                try {
                    msgLogger.info("send post to url:{}, body:{}", url, json);
                    response = httpclient.execute(post);
                    break;
                } catch (Exception e) {
                    if (testIndex == sendLimit) {
                        post.abort();
                        throw e;
                    } else {
                        logger.error("testCount:{}", testIndex, e);
                    }
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                String retString = null == entity ? null : EntityUtils.toString(entity);
                EntityUtils.consume(entity);
                HttpResponseData<String> responseData = new HttpResponseData<>();
                responseData.setStatus(response.getStatusLine().getStatusCode());
                responseData.setBody(retString);
                return responseData;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    /**
     * 禁用链接复用
     */
    public HttpResponseData<String> sendRestfulPostNoReuse(String url, String json, Map<String, String> headerMap, int sendLimit) {
        if (sendLimit < 1) {
            sendLimit = 1;
        }
        try {
            HttpPost post = new HttpPost(url);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    post.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity reqEntity = new StringEntity(json, ContentType.APPLICATION_JSON);
            post.setEntity(reqEntity);
            int testIndex = 0;
            CloseableHttpResponse response = null;
            while (testIndex < sendLimit) {
                testIndex++;
                try {
                    msgLogger.info("send post to url:{}, body:{}", url, json);
                    response = noConnectionReuseHttpclient.execute(post);
                    break;
                } catch (Exception e) {
                    if (testIndex == sendLimit) {
                        post.abort();
                        throw e;
                    } else {
                        logger.error("testCount:{}", testIndex, e);
                    }
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                String retString = EntityUtils.toString(response.getEntity());
                EntityUtils.consume(entity);
                HttpResponseData<String> responseData = new HttpResponseData<>();
                responseData.setStatus(response.getStatusLine().getStatusCode());
                responseData.setBody(retString);
                return responseData;
            } catch (Exception e) {
                logger.error(e.getMessage(), e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public HttpResponseData<byte[]> sendPostBytes(String url, byte[] body) {
        try {
            HttpPost post = new HttpPost(url);
            ByteArrayEntity reqEntity = new ByteArrayEntity(body, ContentType.APPLICATION_OCTET_STREAM);
            post.setEntity(reqEntity);
            post.setConfig(config);
            int testIndex = 0;
            CloseableHttpResponse response = null;
            while (testIndex < 3) {
                testIndex++;
                try {
                    msgLogger.info("send post to url:{}, body.length={}", url, body.length);
                    response = httpclient.execute(post);
                    break;
                } catch (Exception e) {
                    if (testIndex == 3) {
                        post.abort();
                        throw e;
                    } else {
                        logger.error("testCount:{}", testIndex, e);
                    }
                }
            }
            try {
                HttpEntity entity = response.getEntity();
                byte[] retArr = EntityUtils.toByteArray(response.getEntity());
                EntityUtils.consume(entity);
                HttpResponseData<byte[]> responseData = new HttpResponseData<>();
                responseData.setStatus(response.getStatusLine().getStatusCode());
                responseData.setBody(retArr);
                return responseData;
            } catch (Exception e) {
                logger.error("", e);
            } finally {
                response.close();
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
        return null;
    }

    public void sendRestfulPut(String url, String json, Map<String, String> headerMap, int sendLimit) {
        if (sendLimit < 1) {
            sendLimit = 1;
        }
        try {
            HttpPut put = new HttpPut(url);
            if (headerMap != null) {
                for (Map.Entry<String, String> entry : headerMap.entrySet()) {
                    put.setHeader(entry.getKey(), entry.getValue());
                }
            }
            StringEntity reqEntity = new StringEntity(json, ContentType.APPLICATION_JSON);
            put.setEntity(reqEntity);
            put.setConfig(config);
            int testIndex = 0;
            while (testIndex < sendLimit) {
                testIndex++;
                try {
                    msgLogger.info("send put to url:{}, body={}", url, json);
                    httpclient.execute(put);
                    EntityUtils.consume(reqEntity);
                    break;
                } catch (Exception e) {
                    if (testIndex == sendLimit) {
                        put.abort();
                        throw e;
                    } else {
                        logger.error("testCount={}", testIndex, e);
                    }
                }
            }
        } catch (Exception e) {
            logger.error(e.getMessage(), e);
        }
    }

    public void close() {
        try {
            httpclient.close();
        } catch (IOException e) {
            logger.error("webClient close error. {}", e.getMessage(), e);
        }
    }
}
